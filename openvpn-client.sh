#!/bin/bash
# 作者: yuangang
# 管理 OpenVPN 客户端

# 导入公共函数
source ./openvpn-common.sh

manage_clients() {
    check_root
    
    if [ ! -e "$OVPN_CONF" ]; then
        echo "错误: OpenVPN 未安装,请先运行安装脚本"
        exit 1
    fi

    echo "OpenVPN 客户端管理"
    echo
    echo "选择操作:"
    echo "1) 添加新客户端"
    echo "2) 导出已有客户端配置"
    echo "3) 列出所有客户端"
    echo "4) 撤销客户端证书"
    echo "5) 退出"
    
    read -rp "选项: " option
    case "$option" in
        1)  # 添加新客户端
            enter_client_name
            build_client_config
            new_client
            echo "客户端 $client 添加成功"
            echo "配置文件位置: $export_dir$client.ovpn"
            ;;
        2)  # 导出配置
            check_clients
            select_client_to export
            new_client
            echo "客户端 $client 配置已导出"
            echo "配置文件位置: $export_dir$client.ovpn"
            ;;
        3)  # 列出客户端
            print_check_clients
            check_clients
            echo
            show_clients
            print_client_total
            ;;
        4)  # 撤销证书
            check_clients
            select_client_to revoke
            confirm_revoke_client
            if [[ "$revoke" =~ ^[yY]$ ]]; then
                print_revoke_client
                revoke_client_ovpn
                print_client_revoked
            else
                print_client_revocation_aborted
            fi
            ;;
        5)  # 退出
            exit 0
            ;;
        *)
            echo "无效选项"
            exit 1
            ;;
    esac
}

# 运行客户端管理
manage_clients 