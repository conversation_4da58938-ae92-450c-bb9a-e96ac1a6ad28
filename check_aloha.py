import asyncio
import aiohttp
import json
from typing import List


async def check_user_aloha(session: aiohttp.ClientSession, user_id: str) -> dict:
    url = f'http://api-inner.finkapp.cn/user/match/fun/execute'
    params = {
        'className': 'com.finka.base.relation.service.schedule.aloha.AlohaTaskService',
        'methodName': 'checkUserAloha',
        'params': user_id
    }

    try:
        async with session.get(url, params=params) as response:
            if response.status == 200:
                return await response.json()
            else:
                return {'error': f'HTTP {response.status}'}
    except Exception as e:
        return {'error': str(e)}


async def process_users(user_ids: List[str], concurrency: int = 2):
    async with aiohttp.ClientSession() as session:
        tasks = []
        results = []

        # 将用户ID分批处理，每批concurrency个
        for i in range(0, len(user_ids), concurrency):
            batch = user_ids[i:i + concurrency]
            batch_tasks = [check_user_aloha(session, uid) for uid in batch]
            batch_results = await asyncio.gather(*batch_tasks)
            results.extend(batch_results)

            # 简单的进度显示
            print(
                f'已处理 {min(i + concurrency, len(user_ids))}/{len(user_ids)} 个用户')

            # 添加短暂延迟避免请求过于频繁
            await asyncio.sleep(0.1)

        return results


def save_results(results: List[dict], output_file: str):
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)


def main():
    # 读取用户ID
    with open('unique_users.txt', 'r') as f:
        user_ids = [line.strip() for line in f if line.strip()]

    print(f'总共读取到 {len(user_ids)} 个用户ID')

    # 运行异步任务
    results = asyncio.run(process_users(user_ids))

    # 保存结果
    output_file = 'aloha_check_results.json'
    save_results(results, output_file)
    print(f'结果已保存到 {output_file}')


if __name__ == '__main__':
    main()




