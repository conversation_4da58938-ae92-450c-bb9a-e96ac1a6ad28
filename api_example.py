# api_example.py
# 作者: yuangang
# 说明: 演示 django-ninja 的基本用法，包括模型、Schema、API 路由和视图

from django_ninja import NinjaAPI, Schema, Query, File, UploadedFile
from typing import List, Optional, Dict, Any
from django.db import models
from django.shortcuts import get_object_or_404
from django.contrib.auth.models import User
from ninja.security import HttpBearer
from ninja.pagination import paginate
from ninja.errors import HttpError
import asyncio
from django.core.cache import cache
from django.http import HttpResponse
import json
import os

# 确保uploads目录存在
os.makedirs("uploads", exist_ok=True)

# 定义一个简单的模型


class Book(models.Model):
    title = models.CharField(max_length=100)
    author = models.CharField(max_length=50)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title

# 定义用于序列化的 Schema


class BookSchema(Schema):
    id: int
    title: str
    author: str
    created_at: str
    updated_at: str


class BookCreateSchema(Schema):
    title: str
    author: str


class BookUpdateSchema(Schema):
    title: Optional[str] = None
    author: Optional[str] = None

# 分页参数 Schema


class PaginationParams(Schema):
    page: int = 1
    page_size: int = 10

# 认证类


class AuthBearer(HttpBearer):
    def authenticate(self, request, token):
        if token == "supersecret":
            return token

# 自定义响应 Schema


class CustomResponse(Schema):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None


# 创建 Ninja API 实例
api = NinjaAPI(auth=AuthBearer(), title="Book API", version="1.0.0",
               description="A simple API for managing books")

# 中间件示例


@api.middleware
def add_custom_header(request, call_next):
    response = call_next(request)
    response["X-Custom-Header"] = "Custom Value"
    return response

# 获取所有书籍的接口，支持分页和过滤


@api.get("/books", response=List[BookSchema])
@paginate
def list_books(request, page: int = 1, page_size: int = 10, author: Optional[str] = None):
    # 查询所有书籍，支持按作者过滤
    qs = Book.objects.all()
    if author:
        qs = qs.filter(author__icontains=author)
    return qs

# 创建新书籍的接口


@api.post("/books", response=BookSchema)
def create_book(request, data: BookCreateSchema):
    # 创建新书籍
    book = Book.objects.create(**data.dict())
    return book

# 获取单本书籍的接口


@api.get("/books/{book_id}", response=BookSchema)
def get_book(request, book_id: int):
    try:
        book = Book.objects.get(id=book_id)
        return book
    except Book.DoesNotExist:
        raise HttpError(404, "未找到该书籍")

# 更新书籍的接口


@api.put("/books/{book_id}", response=BookSchema)
def update_book(request, book_id: int, data: BookUpdateSchema):
    book = get_object_or_404(Book, id=book_id)
    for key, value in data.dict(exclude_unset=True).items():
        setattr(book, key, value)
    book.save()
    return book

# 删除书籍的接口


@api.delete("/books/{book_id}")
def delete_book(request, book_id: int):
    book = get_object_or_404(Book, id=book_id)
    book.delete()
    return {"success": True}

# 文件上传示例


@api.post("/upload", response=CustomResponse)
def upload_file(request, file: UploadedFile = File(...)):
    # 处理文件上传
    with open(f"uploads/{file.name}", "wb") as f:
        f.write(file.read())
    return CustomResponse(success=True, message="文件上传成功", data={"filename": file.name})

# 异步视图示例


@api.get("/async", response=CustomResponse)
async def async_view(request):
    await asyncio.sleep(1)
    return CustomResponse(success=True, message="异步请求成功")

# 缓存示例


@api.get("/cached", response=CustomResponse)
def cached_view(request):
    cached_data = cache.get("cached_data")
    if cached_data:
        return CustomResponse(success=True, message="从缓存获取数据", data=cached_data)
    data = {"key": "value"}
    cache.set("cached_data", data, 60)
    return CustomResponse(success=True, message="数据已缓存", data=data)

# 自定义异常处理


@api.exception_handler(Exception)
def custom_exception_handler(request, exc):
    return HttpResponse(json.dumps({"detail": str(exc)}), status=500)

# urls.py 中的配置示例
# from django.urls import path
# from .api_example import api
# urlpatterns = [
#     path("api/", api.urls),  # 这样就可以通过 /api/books 访问接口
# ]

# 你可以通过 Django Admin 添加 Book 数据，也可以通过 API 进行增删查操作

# 运行说明:
# 1. 确保已安装 Django 和 django-ninja: pip install django django-ninja
# 2. 将 api_example.py 放在 Django 项目的 app 目录下
# 3. 在 urls.py 中引入 api 并配置路由
# 4. 运行 python manage.py makemigrations 和 python manage.py migrate 创建表
# 5. 启动 Django 服务: python manage.py runserver
# 6. 访问 /api/docs 查看 API 文档
