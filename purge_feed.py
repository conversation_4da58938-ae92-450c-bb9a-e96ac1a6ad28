import http.client
import json
import time


def batch_process_ids(start_id=564000, end_id=7900000, batch_size=20):
    current_id = start_id
    while current_id <= end_id:
        # 获取当前批次的ID
        batch_ids = list(range(current_id, min(
            current_id + batch_size, end_id + 1)))
        # 将ID列表转换为逗号分隔的字符串
        id_str = ','.join(map(str, batch_ids))

        # 发送请求
        conn = http.client.HTTPConnection("10.250.24.61", 9999)
        payload = json.dumps({
            "jobId": 134,
            "executorHandler": "feedCleanRunner.purgeProfileCache",
            "executorBlockStrategy": "SERIAL_EXECUTION",
            "executorTimeout": 0,
            "logId": 1,
            "logDateTime": int(time.time() * 1000),
            "glueType": "BEAN",
            "glueUpdatetime": int(time.time() * 1000),
            "broadcastIndex": 0,
            "broadcastTotal": 0,
            "executorParams": id_str
        })

        headers = {
            'XXL-JOB-ACCESS-TOKEN': 'K8ei_cq1pJ8hjz8s)',
            'Content-Type': 'application/json',
        }

        try:
            conn.request("POST", "/run", payload, headers)
            res = conn.getresponse()
            data = res.read()
            print(
                f"处理ID {current_id} 到 {current_id + len(batch_ids) - 1} 的响应: {data.decode('utf-8')}")
        except Exception as e:
            print(
                f"处理ID {current_id} 到 {current_id + len(batch_ids) - 1} 时发生错误: {str(e)}")
        finally:
            conn.close()

        # 更新当前ID
        current_id += batch_size
        # 添加短暂延迟避免请求过于频繁
        time.sleep(0.2)


if __name__ == '__main__':
    batch_process_ids()
