def validate_user_ids(file_path):
    count = 0
    with open(file_path, 'r') as f:
        for i, line in enumerate(f):
            line = line.strip()
            if not line.isdigit():
                print(f"Line {i+1}: '{line}' is not a valid user ID.")
                return False
            count += 1
    print(f"Total valid user IDs: {count}")
    return True

if __name__ == "__main__":
    file_path = "user_ids.txt"
    if validate_user_ids(file_path):
        print(f"Validation completed for {file_path}.")
    else:
        print(f"Validation failed for {file_path}.")