#!/usr/bin/env python3
import json
import sys


def parse_log_line(line):
    import re
    # 先尝试解析JSON格式
    try:
        data = json.loads(line)
        body = data.get('body', '')
        if not body:
            # 直接在顶层查找userId
            user_id = data.get('userId')
            if user_id:
                return {
                    'userId': user_id,
                    'toUserId': data.get('toUserId'),
                    'timestamp': data.get('__TIMESTAMP__'),
                    'level': data.get('level'),
                    'relationType': None,
                    'exception': None
                }
            return None
        if 'message:' in body:
            message_start = body.find('message:') + len('message:')
            message_str = body[message_start:].split('\n')[0].strip()
            try:
                message_data = json.loads(message_str)
                return {
                    'userId': message_data.get('userId'),
                    'toUserId': message_data.get('toUserId'),
                    'timestamp': data.get('__TIMESTAMP__'),
                    'level': data.get('level'),
                    'relationType': None,
                    'exception': None
                }
            except json.JSONDecodeError:
                # message字段不是json，尝试正则提取userId
                m = re.search(r'userId[":：]?\s*(\d+)', message_str)
                if m:
                    return {
                        'userId': m.group(1),
                        'toUserId': None,
                        'timestamp': data.get('__TIMESTAMP__'),
                        'level': data.get('level'),
                        'relationType': None,
                        'exception': None
                    }
                return None
    except json.JSONDecodeError:
        pass
    # 普通文本格式解析
    # 先尝试直接提取userId
    m = re.search(r'userId[":：]?\s*(\d+)', line)
    user_id = m.group(1) if m else None
    # 匹配主信息行
    m2 = re.search(
        r'userId[:：]?\s*(\d+)[，,]\s*关系类型[:：]?\s*([\w]+)[，,].*?异常', line)
    if m2:
        relation_type = m2.group(2)
        # 尝试提取异常类名和信息
        exception_match = re.search(r'(\w+(?:\.\w+)*Exception):\s*(.*)', line)
        exception = exception_match.group(
            1) + ': ' + exception_match.group(2) if exception_match else None
        return {
            'userId': m2.group(1),
            'toUserId': None,
            'timestamp': None,
            'level': None,
            'relationType': relation_type,
            'exception': exception
        }
    # 如果是异常堆栈行，单独提取异常
    exception_match = re.search(r'(\w+(?:\.\w+)*Exception):\s*(.*)', line)
    if exception_match:
        return {
            'userId': user_id,
            'toUserId': None,
            'timestamp': None,
            'level': None,
            'relationType': None,
            'exception': exception_match.group(1) + ': ' + exception_match.group(2)
        }
    # 只要能提取到userId就返回
    if user_id:
        return {
            'userId': user_id,
            'toUserId': None,
            'timestamp': None,
            'level': None,
            'relationType': None,
            'exception': None
        }
    return None


def main():
    if len(sys.argv) > 3:
        print("使用方法: python3 parse_json_log.py [log_file] [output_file]")
        print("如果不指定log_file，将从标准输入读取")
        print("如果不指定output_file，将输出到标准输出")
        sys.exit(1)

    # 从标准输入读取或从文件读取
    try:
        input_source = sys.stdin if len(
            sys.argv) == 1 else open(sys.argv[1], 'r')
    except FileNotFoundError:
        print(f"错误: 找不到文件 '{sys.argv[1]}'")
        sys.exit(1)
    except PermissionError:
        print(f"错误: 没有权限读取文件 '{sys.argv[1]}'")
        sys.exit(1)

    # 设置输出目标
    try:
        output_file = open(sys.argv[2], 'w') if len(
            sys.argv) == 3 else sys.stdout
    except PermissionError:
        print(f"错误: 没有权限写入文件 '{sys.argv[2]}'")
        if input_source is not sys.stdin:
            input_source.close()
        sys.exit(1)
    except Exception as e:
        print(f"创建输出文件时发生错误: {str(e)}")
        if input_source is not sys.stdin:
            input_source.close()
        sys.exit(1)

    try:
        for line in input_source:
            result = parse_log_line(line.strip())
            if result and result.get('userId'):
                output_file.write(f"{result['userId']}\n")
    except KeyboardInterrupt:
        print("\n程序被用户中断", file=sys.stderr)
    except Exception as e:
        print(f"处理日志时发生错误: {str(e)}", file=sys.stderr)
    finally:
        if input_source is not sys.stdin:
            input_source.close()
        if output_file is not sys.stdout:
            output_file.close()


if __name__ == '__main__':
    main()
