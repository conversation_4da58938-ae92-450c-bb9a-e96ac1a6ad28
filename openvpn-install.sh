#!/bin/bash
# 作者: yuangang
# 安装 OpenVPN 服务器

# 导入公共函数
source ./openvpn-common.sh

install_openvpn() {
    check_root
    check_shell
    check_kernel
    check_os
    check_os_ver
    check_tun
    check_nftables
    install_wget
    install_iproute

    # 自动模式
    auto=1
    detect_ip
    check_nat_ip
    show_config
    protocol=udp
    port=1194
    dns=1
    client=client

    # 开始安装
    show_start_setup
    disable_limitnproc
    install_pkgs
    install_easyrsa
    create_pki_and_certs
    create_server_config
    update_sysctl
    update_selinux
    create_client_common
    start_openvpn_service
    new_client
    
    echo
    echo "OpenVPN 安装完成!"
    echo "客户端配置文件位置: $export_dir$client.ovpn"
}

# 运行安装
install_openvpn 