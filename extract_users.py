import csv

# 初始化集合用于存储唯一的用户ID
unique_users = set()

# 读取CSV文件
with open('user.csv', 'r') as file:
    # 逐行读取文件
    for line in file:
        # 分割每行，提取UserId和ToUserId
        if 'UserId:' in line and 'ToUserId:' in line:
            # 提取数字部分
            parts = line.split(',')
            user_id = parts[0].split(':')[1].strip()
            to_user_id = parts[1].split(':')[1].strip()
            
            # 添加到集合中（自动去重）
            unique_users.add(user_id)
            unique_users.add(to_user_id)

# 将结果写入新文件
with open('unique_users.txt', 'w') as output_file:
    # 将集合转换为排序列表
    sorted_users = sorted(list(unique_users), key=int)
    # 写入文件
    for user_id in sorted_users:
        output_file.write(f'{user_id}\n')

print(f'Total unique users: {len(unique_users)}')


