#!/bin/bash
# 作者: yuangang
# OpenVPN 公共函数

# 设置全局变量
OVPN_CONF="/etc/openvpn/server/server.conf"
export_dir="/data/openvpn/"

# 错误处理函数
exiterr()  { echo "Error: $1" >&2; exit 1; }
exiterr2() { exiterr "'apt-get install' failed."; }
exiterr3() { exiterr "'yum install' failed."; }
exiterr4() { exiterr "'zypper install' failed."; }

# IP 地址检查
check_ip() {
    IP_REGEX='^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$'
    printf '%s' "$1" | tr -d '\n' | grep -Eq "$IP_REGEX"
}

# 基本检查函数
check_root() {
    if [ "$(id -u)" != 0 ]; then
        exiterr "This installer must be run as root. Try 'sudo bash $0'"
    fi
}

check_shell() {
    if readlink /proc/$$/exe | grep -q "dash"; then
        exiterr 'This installer needs to be run with "bash", not "sh".'
    fi
}

check_kernel() {
    if [[ $(uname -r | cut -d "." -f 1) -eq 2 ]]; then
        exiterr "The system is running an old kernel, which is incompatible with this installer."
    fi
}

# 系统检查函数
check_os() {
    if grep -qs "ubuntu" /etc/os-release; then
        os="ubuntu"
        os_version=$(grep 'VERSION_ID' /etc/os-release | cut -d '"' -f 2 | tr -d '.')
        group_name="nogroup"
    elif [[ -e /etc/debian_version ]]; then
        os="debian"
        os_version=$(grep -oE '[0-9]+' /etc/debian_version | head -1)
        group_name="nogroup"
    elif [[ -e /etc/almalinux-release || -e /etc/rocky-release || -e /etc/centos-release ]]; then
        os="centos"
        os_version=$(grep -shoE '[0-9]+' /etc/almalinux-release /etc/rocky-release /etc/centos-release | head -1)
        group_name="nobody"
    else
        exiterr "This installer seems to be running on an unsupported distribution."
    fi
}

check_os_ver() {
    if [[ "$os" == "ubuntu" && "$os_version" -lt 2004 ]]; then
        exiterr "Ubuntu 20.04 or higher is required to use this installer."
    fi
    if [[ "$os" == "debian" && "$os_version" -lt 10 ]]; then
        exiterr "Debian 10 or higher is required to use this installer."
    fi
    if [[ "$os" == "centos" && "$os_version" -lt 7 ]]; then
        exiterr "CentOS 7 or higher is required to use this installer."
    fi
}

check_tun() {
    if [[ ! -e /dev/net/tun ]] || ! ( exec 7<>/dev/net/tun ) 2>/dev/null; then
        exiterr "The system does not have the TUN device available."
    fi
}

# IP 检测函数
find_public_ip() {
    ip_url1="http://ipv4.icanhazip.com"
    ip_url2="https://ipinfo.io/ip"
    get_public_ip=$(grep -m 1 -oE '^[0-9]{1,3}(\.[0-9]{1,3}){3}$' <<< "$(wget -T 10 -t 1 -4qO- "$ip_url1" || curl -m 10 -4Ls "$ip_url1")")
    if ! check_ip "$get_public_ip"; then
        get_public_ip=$(grep -m 1 -oE '^[0-9]{1,3}(\.[0-9]{1,3}){3}$' <<< "$(wget -T 10 -t 1 -4qO- "$ip_url2" || curl -m 10 -4Ls "$ip_url2")")
    fi
}

detect_ip() {
    if [[ $(ip -4 addr | grep inet | grep -vEc '127(\.[0-9]{1,3}){3}') -eq 1 ]]; then
        ip=$(ip -4 addr | grep inet | grep -vE '127(\.[0-9]{1,3}){3}' | cut -d '/' -f 1 | grep -oE '[0-9]{1,3}(\.[0-9]{1,3}){3}')
    else
        ip=$(ip -4 route get 1 | sed 's/ uid .*//' | awk '{print $NF;exit}' 2>/dev/null)
        if ! check_ip "$ip"; then
            find_public_ip
            ip_match=0
            if [ -n "$get_public_ip" ]; then
                ip_list=$(ip -4 addr | grep inet | grep -vE '127(\.[0-9]{1,3}){3}' | cut -d '/' -f 1 | grep -oE '[0-9]{1,3}(\.[0-9]{1,3}){3}')
                while IFS= read -r line; do
                    if [ "$line" = "$get_public_ip" ]; then
                        ip_match=1
                        ip="$line"
                    fi
                done <<< "$ip_list"
            fi
            if [ "$ip_match" = 0 ]; then
                ip_num=1
                ip=$(ip -4 addr | grep inet | grep -vE '127(\.[0-9]{1,3}){3}' | cut -d '/' -f 1 | grep -oE '[0-9]{1,3}(\.[0-9]{1,3}){3}' | sed -n "$ip_num"p)
            fi
        fi
    fi
    if ! check_ip "$ip"; then
        exiterr "Could not detect this server's IP address."
    fi
}

check_nat_ip() {
    if printf '%s' "$ip" | grep -qE '^(10|127|172\.(1[6-9]|2[0-9]|3[0-1])|192\.168|169\.254)\.'; then
        find_public_ip
        if ! check_ip "$get_public_ip"; then
            exiterr "Could not detect this server's public IP."
        else
            public_ip="$get_public_ip"
        fi
    fi
}

# 安装相关函数
install_wget() {
    if ! hash wget 2>/dev/null && ! hash curl 2>/dev/null; then
        export DEBIAN_FRONTEND=noninteractive
        (
            set -x
            apt-get -yqq update || apt-get -yqq update
            apt-get -yqq install wget >/dev/null
        ) || exiterr2
    fi
}

install_iproute() {
    if ! hash ip 2>/dev/null; then
        if [ "$os" = "debian" ] || [ "$os" = "ubuntu" ]; then
            export DEBIAN_FRONTEND=noninteractive
            (
                set -x
                apt-get -yqq update || apt-get -yqq update
                apt-get -yqq install iproute2 >/dev/null
            ) || exiterr2
        else
            (
                set -x
                yum -y -q install iproute >/dev/null
            ) || exiterr3
        fi
    fi
}

install_pkgs() {
    if [[ "$os" = "debian" || "$os" = "ubuntu" ]]; then
        export DEBIAN_FRONTEND=noninteractive
        (
            set -x
            apt-get -yqq update || apt-get -yqq update
            apt-get -yqq --no-install-recommends install openvpn >/dev/null
            apt-get -yqq install openssl ca-certificates $firewall >/dev/null
        ) || exiterr2
    elif [[ "$os" = "centos" ]]; then
        if grep -qs "Amazon Linux release 2 " /etc/system-release; then
            amazon-linux-extras install epel -y >/dev/null
        else
            yum -y -q install epel-release >/dev/null
        fi
        (
            set -x
            yum -y -q install openvpn openssl ca-certificates tar $firewall >/dev/null 2>&1
        ) || exiterr3
    fi
}

# 客户端管理函数
set_client_name() {
    client=$(sed 's/[^0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ_-]/_/g' <<< "$unsanitized_client")
}

new_client() {
    # 确保导出目录存在
    if [ ! -d "$export_dir" ]; then
        mkdir -p "$export_dir"
    fi

    # 生成客户端配置
    {
        cat /etc/openvpn/server/client-common.txt
        echo "<ca>"
        cat /etc/openvpn/server/easy-rsa/pki/ca.crt
        echo "</ca>"
        echo "<cert>"
        sed -ne '/BEGIN CERTIFICATE/,$ p' /etc/openvpn/server/easy-rsa/pki/issued/"$client".crt
        echo "</cert>"
        echo "<key>"
        cat /etc/openvpn/server/easy-rsa/pki/private/"$client".key
        echo "</key>"
        echo "<tls-crypt>"
        sed -ne '/BEGIN OpenVPN Static key/,$ p' /etc/openvpn/server/tc.key
        echo "</tls-crypt>"
    } > "$export_dir$client".ovpn
    chmod 600 "$export_dir$client".ovpn
}

build_client_config() {
    cd /etc/openvpn/server/easy-rsa/ || exit 1
    (
        set -x
        ./easyrsa --batch --days=366 build-client-full "$client" nopass >/dev/null 2>&1
    )
}

show_clients() {
    tail -n +2 /etc/openvpn/server/easy-rsa/pki/index.txt | grep "^V" | cut -d '=' -f 2 | nl -s ') '
}

check_clients() {
    num_of_clients=$(tail -n +2 /etc/openvpn/server/easy-rsa/pki/index.txt | grep -c "^V")
    if [[ "$num_of_clients" = 0 ]]; then
        echo "没有现有客户端!"
        exit 1
    fi
}

select_client_to() {
    echo
    echo "选择要$1的客户端:"
    show_clients
    read -rp "客户端编号: " client_num
    [ -z "$client_num" ] && abort_and_exit
    until [[ "$client_num" =~ ^[0-9]+$ && "$client_num" -le "$num_of_clients" ]]; do
        echo "$client_num: 无效选择."
        read -rp "客户端编号: " client_num
        [ -z "$client_num" ] && abort_and_exit
    done
    client=$(tail -n +2 /etc/openvpn/server/easy-rsa/pki/index.txt | grep "^V" | cut -d '=' -f 2 | sed -n "$client_num"p)
}

revoke_client_ovpn() {
    cd /etc/openvpn/server/easy-rsa/ || exit 1
    (
        set -x
        ./easyrsa --batch revoke "$client" >/dev/null 2>&1
        ./easyrsa --batch --days=3650 gen-crl >/dev/null 2>&1
    )
    rm -f /etc/openvpn/server/crl.pem
    cp /etc/openvpn/server/easy-rsa/pki/crl.pem /etc/openvpn/server/crl.pem
    chmod o+x /etc/openvpn/server/
    chown nobody:"$group_name" /etc/openvpn/server/crl.pem
    rm -f "$export_dir$client".ovpn
}

# 服务器配置函数
create_server_config() {
echo "local $ip
port $port
proto $protocol
dev tun
ca ca.crt
cert server.crt
key server.key
dh dh.pem
auth SHA256
tls-crypt tc.key
topology subnet
server ********** *************" > "$OVPN_CONF"

echo 'ifconfig-pool-persist ipp.txt' >> "$OVPN_CONF"
create_dns_config

echo "keepalive 10 120
cipher AES-128-GCM
user nobody
group $group_name
persist-key
persist-tun
verb 3
crl-verify crl.pem" >> "$OVPN_CONF"

    if [[ "$protocol" = "udp" ]]; then
        echo "explicit-exit-notify" >> "$OVPN_CONF"
    fi
}

create_client_common() {
    [[ -n "$public_ip" ]] && ip="$public_ip"
    echo "client
dev tun
proto $protocol
remote $ip $port
resolv-retry infinite
nobind
persist-key
persist-tun
remote-cert-tls server
auth SHA256
cipher AES-128-GCM
ignore-unknown-option block-outside-dns block-ipv6
verb 3" > /etc/openvpn/server/client-common.txt
}

create_dns_config() {
    if grep '^nameserver' "/etc/resolv.conf" | grep -qv '**********' ; then
        resolv_conf="/etc/resolv.conf"
    else
        resolv_conf="/run/systemd/resolve/resolv.conf"
    fi
    grep -v '^#\|^;' "$resolv_conf" | grep '^nameserver' | grep -v '**********' | grep -oE '[0-9]{1,3}(\.[0-9]{1,3}){3}' | while read line; do
        echo "push \"dhcp-option DNS $line\"" >> "$OVPN_CONF"
    done
}

# 其他辅助函数
show_config() {
    echo
    printf '%s' "服务器 IP: "
    [ -n "$public_ip" ] && printf '%s\n' "$public_ip" || printf '%s\n' "$ip"
    echo "端口: UDP/1194"
    echo "客户端名称: client"
}

start_openvpn_service() {
    systemctl enable --now <EMAIL> >/dev/null 2>&1
}