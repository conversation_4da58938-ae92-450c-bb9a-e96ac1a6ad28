#!/bin/sh

# --- 配置 ---
# 从环境变量获取配置，如果未提供则使用默认值或报错退出
: "${COS_SECRET_ID?Must set COS_SECRET_ID to a non-empty value}"
: "${COS_SECRET_KEY?Must set COS_SECRET_KEY to a non-empty value}"
: "${COS_BUCKET?Must set COS_BUCKET to a non-empty value}"
: "${COS_REGION?Must set COS_REGION to a non-empty value}"
: "${WATCH_DIR?Must set WATCH_DIR to a non-empty value}"
: "${POD_NAME?Must set POD_NAME to a non-empty value}"

# 允许通过环境变量覆盖的配置，提供健壮的默认值
: "${THROTTLE_SECONDS:=10}"

# --- 初始化 ---
echo "--- Sidecar Watcher starting up ---"
echo "Watching directory: ${WATCH_DIR}"
echo "Throttling interval: ${THROTTLE_SECONDS} seconds"
echo "-------------------------------------"

# 配置 coscmd
echo "Configuring coscmd..."
coscmd config -a "$COS_SECRET_ID" -s "$COS_SECRET_KEY" -b "$COS_BUCKET" -r "$COS_REGION"
echo "Configuration complete."

# 目标 COS 路径
DEST_PATH="/dump/${POD_NAME}/"

# 要监控和上传的文件模式 (正则表达式)
FILE_PATTERN_REGEX='.*(Dubbo_JStack|hprof).*'
# 初始化上次上传时间戳为一个很早的时间（纪元初），确保第一个事件能被立即处理
last_upload_timestamp=0

echo "$(date): Ready and waiting for file changes..."

# --- 主循环 ---
# 使用 inotifywait 的监控模式(-m)，并用管道将输出喂给 while read 循环
# 这种方式比在循环内部重复调用 inotifywait 更高效、更健壮
inotifywait -m -q -r -e close_write,moved_to --format '%w%f' --include "$FILE_PATTERN_REGEX" "$WATCH_DIR" | while read -r file; do
    
    echo "$(date): Detected event on target file: ${file}"
    
    current_timestamp=$(date +%s)
    time_diff=$((current_timestamp - last_upload_timestamp))
    
    # 检查是否在冷却期内
    if [ "$time_diff" -lt "$THROTTLE_SECONDS" ]; then
        # 计算并等待剩余时间
        remaining_wait=$((THROTTLE_SECONDS - time_diff))
        echo "$(date): In cooldown period. Waiting for ${remaining_wait}s..."
        sleep "$remaining_wait"
    fi
    
    # 无论是立即执行还是等待后执行，最终都会到达这里
    echo "$(date): Ready to sync ${file}."
    
    # 在任务开始前更新时间戳，为下一次事件启动一个新的时间窗口，防止任务堆积或并发执行
    last_upload_timestamp=$(date +%s)
    
    # 执行上传，直接上传被事件触发的那个特定文件
    timeout 120s coscmd upload "${file}" "${DEST_PATH}"
    upload_exit_code=$?
    if [ $upload_exit_code -eq 0 ]; then
        echo "$(date): Sync of ${file} successful."
        echo "$(date): Deleting local file: ${file}"
        rm -f "${file}" # 上传成功后删除本地文件
    elif [ $upload_exit_code -eq 124 ]; then
        # 124是timeout命令超时的退出码
        echo "$(date): Sync of ${file} failed: Timed out."
        last_upload_timestamp=0
    else
        echo "$(date): Sync of ${file} failed."
        # 如果上传失败，立即重置时间戳。
        last_upload_timestamp=0
    fi
    echo "-------------------------------------"
done