#!/bin/sh
# 确保关键环境变量存在
if [ -z "$COS_BUCKET" ] || [ -z "$WATCH_DIR" ] || [ -z "$POD_NAME" ]; then
    echo "$(date): [preStop] ERROR: Missing critical environment variables (COS_BUCKET, WATCH_DIR, or POD_NAME). Exiting."
    exit 1
fi

# 要监控和上传的文件模式 (正则表达式)
FILE_PATTERN_REGEX='.*(Dubbo_JStack|hprof).*'

# --- 主逻辑 ---
DEST_PATH="/dump/${POD_NAME}/"

echo "$(date): [preStop] Hook triggered. Performing final, reliable sync."
echo "$(date): [preStop] Uploading matching files from $WATCH_DIR to COS path $DEST_PATH"

# 在上传命令中加入 --include 参数，确保只上传我们关心的文件
#
# -rs: 递归同步，跳过已存在且大小相同的文件
# --force: 强制上传，对于确保最后状态的一致性很有用
# --include "$FILE_PATTERN_GLOB": 只包含匹配这个glob模式的文件
coscmd upload -rs --force --include "$FILE_PATTERN_REGEX" "$WATCH_DIR/" "$DEST_PATH"
# -------------------------------------------------------- #

if [ $? -eq 0 ]; then
    echo "$(date): [preStop] Final sync successful."
else
    # 即使失败，我们也只是记录日志，因为无论如何Pod都将被终止
    echo "$(date): [preStop] WARNING: Final sync failed. Some files might be lost. The pod will be terminated anyway."
fi

# 通常 preStop 钩子执行完后，kubelet 就会发送 SIGTERM。
# 这里的 sleep 意义不大，但如果想给网络I/O一点点缓冲，可以保留一个很短的值。
# sleep 1
echo "$(date): [preStop] Hook finished."