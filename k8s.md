nginx.ingress.kubernetes.io/server-snippet  # key
location /verification.txt {  default_type text/plain;  return 200 "9er5742t19j9le8sndpc2caw3puidhog";} # value


        location /googlecc57cedf7f7b847f.html {
                return 200 'google-site-verification: googlecc57cedf7f7b847f.html';
        }
        location /.well-known/apple-app-site-association {
            default_type application/json;
            return 200 '{"applinks":{"apps":[],"details":[{"appID":"Z6ZMU24UQ6.com.wealoha.alohapre","paths":["*"]}]}}';
        }



        在ingress 中添加自定义片段nginx.ingress.kubernetes.io/server-snippet 报错 admission webhook "validate.nginx.ingress.kubernetes.io" denied the request: nginx.ingress.kubernetes.io/server-snippet annotation cannot be used. Snippet directives are disabled by the Ingress administrator
nginx-ingress-nginx-controller的 allow-snippet-annotations: “true"
nginx.ingress.kubernetes.io/server-snippet: |
  if ( $request_uri ~*  (/doc.html|/swagger)) {
           return 403;
  }
 




kubectl apply -f /data/jenkins/workspace/relation-web/finka-backend-scripts/intl-build/template/k8s/monitor.yaml
kubectl apply -f /data/jenkins/workspace/user-web/finka-backend-scripts/intl-build/template/k8s/monitor.yaml
kubectl apply -f /data/jenkins/workspace/vas-web/finka-backend-scripts/intl-build/template/k8s/monitor.yaml
kubectl apply -f /data/jenkins/workspace/biz-base-web/finka-backend-scripts/intl-build/template/k8s/monitor.yaml
kubectl apply -f /data/jenkins/workspace/payment-web/finka-backend-scripts/intl-build/template/k8s/monitor.yaml


helm upgrade --install rancher rancher-stable/rancher \
  --namespace cattle-system \
  --set hostname=rancher-intl-test.wowkaka.cn \
  --set ingress.tls.source=secret \
  --set replicas=1 \
  --set ingress.tls.secretName=tls-rancher-ingress \

https://github.com/rancher/rancher-cleanup
kubectl create -f deploy/rancher-cleanup.yaml
kubectl  -n kube-system logs -l job-name=cleanup-job  -f
kubectl create -f deploy/verify.yaml
kubectl  -n kube-system logs -l job-name=verify-job  -f

  kubectl get secret --namespace cattle-system bootstrap-secret -o go-template='{{.data.bootstrapPassword|base64decode}}{{"\n"}}'
  44zrbfwd97259djp5twmpx8mlskhgqv4wxjhnjnd662t92256g2cp7

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    field.cattle.io/publicEndpoints: >-
      [{"addresses":[""],"port":80,"protocol":"HTTP","serviceName":"finka-backend-dev:h5","ingressName":"finka-backend-dev:h5-internet","hostname":"h5-test.mityou.com","allNodes":false},{"addresses":[""],"port":80,"protocol":"HTTP","serviceName":"finka-backend-dev:admin-h5","ingressName":"finka-backend-dev:h5-internet","hostname":"admin-h5-test.mityou.com","allNodes":false}]
    kubernetes.io/ingress.class: nginx-ingress-internet
    kubernetes.io/ingress.rule-mix: 'true'
    nginx.ingress.kubernetes.io/configuration-snippet: |
      if ($request_method = OPTIONS) {
        return 204;
      }
      add_header Access-Control-Allow-Origin *;
      add_header Access-Control-Allow-Methods *;
      add_header Access-Control-Allow-Headers *;
      add_header Access-Control-Allow-Credentials true;
      add_header Access-Control-Max-Age 86400;
    nginx.ingress.kubernetes.io/server-snippet: '        location /googlecc57cedf7f7b847f.html {                 return 200 ''google-site-verification: googlecc57cedf7f7b847f.html'';         }         location /.well-known/apple-app-site-association {             default_type application/json;             return 200 ''{"applinks":{"apps":[],"details":[{"appID":"Z6ZMU24UQ6.com.wealoha.alohapre","paths":["*"]}]}}'';         }'
  creationTimestamp: '2024-07-14T08:11:42Z'


      nginx.ingress.kubernetes.io/server-snippet: '        location /google9429f39f070e0754.html
      {                 return 200 ''google-site-verification: google9429f39f070e0754.html'';         }         location
      /.well-known/apple-app-site-association {             default_type application/json;             return
      200 ''{"applinks":{"apps":[],"details":[{"appID":"Z6ZMU24UQ6.com.wealoha.alohapre","paths":["*"]}]}}'';         }'
    

    nginx.ingress.kubernetes.io/server-snippet: |
      location /google9429f39f070e0754.html {                 
        return 200 'google-site-verification: google9429f39f070e0754.html';         
      }
      location /.well-known/apple-app-site-association {
          default_type application/json;
          return 200 '{"applinks":{"apps":[],"details":[{"appID":"GPXLH4QDAP.com.yosocial.yosocial.ios","paths":["*"]}]}}';
      }

---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  namespace: finka-backend-dev
  name: finka-intl-user-web
  labels:
    app: finka-intl-user-web
    team: team-fleeper
    release: kube-prometheus
spec:
  jobLabel: app
  endpoints:
    - interval: 15s
      port: http-metrics
      path: /actuator/prometheus
  namespaceSelector:
    matchNames:
      - finka-backend-dev
  sampleLimit: 10000
  selector:
    matchLabels:
      app: finka-intl-user-web
      team: team-fleeper
  targetLabels:
    - team
    - app
---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  namespace: finka-backend-dev
  name: finka-intl-relation-web
  labels:
    app: finka-intl-relation-web
    # release: kube-prometheus
spec:
  jobLabel: app
  endpoints:
    - interval: 15s
      port: http-metrics
      path: /actuator/prometheus
  namespaceSelector:
    matchNames:
      - finka-backend-dev
  sampleLimit: 10000
  selector:
    matchLabels:
      app: finka-intl-relation-web
  targetLabels:
    - app
    - 

---
apiVersion: v1
kind: Service
metadata:
  namespace: finka-backend-pro
  name: blue-device
labels:
  app: blue-device
  team: team-fleeper
spec:
  ports:
    - name: http
      port: 8080
      protocol: TCP
      targetPort: 8000
---
apiVersion: v1
kind: Endpoints
metadata:
  namespace: finka-backend-pro
  name: blue-device
subsets:
  - addresses:
      - ip: ***********
      - ip: ***********
    ports:
      - port: 8000
---

