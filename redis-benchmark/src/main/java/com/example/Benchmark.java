package com.example;

import io.lettuce.core.RedisClient;
import io.lettuce.core.api.StatefulRedisConnection;
import io.lettuce.core.api.sync.RedisCommands;
import io.lettuce.core.support.ConnectionPoolSupport;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

public class Benchmark {

    // --- 可配置参数 ---
    private static final String REDIS_HOST = "localhost";
    private static final int REDIS_PORT = 6379;
    private static final int TOTAL_OPERATIONS = 200000; // 总操作数
    private static final int THREAD_COUNT = 100;       // 并发线程数
    // ---

    public static void main(String[] args) throws Exception {
        System.out.println("Starting Redis Comprehensive Benchmark...");
        System.out.printf("Host: %s, Port: %d, Total Operations: %d, Threads: %d%n",
                REDIS_HOST, REDIS_PORT, TOTAL_OPERATIONS, THREAD_COUNT);
        System.out.println("======================================================================");

        // Jedis 场景
        jedisPoolMultiThread_Optimal();
        jedisPoolMultiThread_Saturated();

        System.out.println("----------------------------------------------------------------------");

        // Lettuce 场景
        lettuceSingleConnectionMultiThread();
        lettuceMultiConnectionMultiThread();
        lettucePoolMultiThread();

        System.out.println("======================================================================");
        System.out.println("Benchmark finished.");
    }

    private static void jedisPoolMultiThread_Optimal() throws InterruptedException {
        int poolSize = 50; // 优化配置：连接数小于线程数
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMaxTotal(poolSize);
        poolConfig.setMaxIdle(poolSize);
        JedisPool jedisPool = new JedisPool(poolConfig, REDIS_HOST, REDIS_PORT);
        runTest("Jedis Pool (Optimal Config)", jedisPool, THREAD_COUNT, TOTAL_OPERATIONS);
        jedisPool.close();
    }

    private static void jedisPoolMultiThread_Saturated() throws InterruptedException {
        int poolSize = THREAD_COUNT; // 饱和配置：连接数等于线程数
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMaxTotal(poolSize);
        poolConfig.setMaxIdle(poolSize);
        JedisPool jedisPool = new JedisPool(poolConfig, REDIS_HOST, REDIS_PORT);
        runTest("Jedis Pool (Saturated Config)", jedisPool, THREAD_COUNT, TOTAL_OPERATIONS);
        jedisPool.close();
    }

    private static void lettuceSingleConnectionMultiThread() throws InterruptedException {
        RedisClient redisClient = RedisClient.create("redis://" + REDIS_HOST + ":" + REDIS_PORT);
        StatefulRedisConnection<String, String> connection = redisClient.connect();
        // Lettuce 的连接是线程安全的
        RedisCommands<String, String> syncCommands = connection.sync();

        runTest("Lettuce Single Connection", syncCommands, THREAD_COUNT, TOTAL_OPERATIONS);

        connection.close();
        redisClient.shutdown();
    }

    private static void lettuceMultiConnectionMultiThread() throws InterruptedException {
        int connectionCount = Runtime.getRuntime().availableProcessors(); // 手动管理少量连接
        RedisClient redisClient = RedisClient.create("redis://" + REDIS_HOST + ":" + REDIS_PORT);
        List<StatefulRedisConnection<String, String>> connections = new ArrayList<>();
        for (int i = 0; i < connectionCount; i++) {
            connections.add(redisClient.connect());
        }
        List<RedisCommands<String, String>> commands = new ArrayList<>();
        for(StatefulRedisConnection<String, String> conn : connections) {
            commands.add(conn.sync());
        }

        runTest("Lettuce Multi Connection (Manual)", commands, THREAD_COUNT, TOTAL_OPERATIONS);

        for (StatefulRedisConnection<String, String> conn : connections) {
            conn.close();
        }
        redisClient.shutdown();
    }

    private static void lettucePoolMultiThread() throws InterruptedException {
        RedisClient redisClient = RedisClient.create("redis://" + REDIS_HOST + ":" + REDIS_PORT);
        GenericObjectPoolConfig<StatefulRedisConnection<String, String>> poolConfig = new GenericObjectPoolConfig<>();
        poolConfig.setMaxTotal(THREAD_COUNT);
        poolConfig.setMaxIdle(THREAD_COUNT);
        GenericObjectPool<StatefulRedisConnection<String, String>> pool = ConnectionPoolSupport.createGenericObjectPool(
                redisClient::connect, poolConfig);

        runTest("Lettuce Pool", pool, THREAD_COUNT, TOTAL_OPERATIONS);

        pool.close();
        redisClient.shutdown();
    }

    // --- 测试执行器 ---

    private static void 
    runTest(String testName, Object resource, int threadCount, int totalOps) throws InterruptedException {
        System.out.printf("Running Test: %-35s | Threads: %-4d | Total Ops: %d%n", testName, threadCount, totalOps);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        AtomicInteger opCounter = new AtomicInteger(0);
        int opsPerThread = totalOps / threadCount;

        long startTime = System.currentTimeMillis();
        if (resource instanceof JedisPool) {
            for (int i = 0; i < threadCount; i++) {
                executor.submit(new JedisTask((JedisPool) resource, opsPerThread, latch));
            }
        } else if (resource instanceof RedisCommands) {
             for (int i = 0; i < threadCount; i++) {
                executor.submit(new LettuceTask((RedisCommands<String, String>) resource, opsPerThread, latch));
            }
        } else if (resource instanceof List) { // Lettuce Multi Connection
            List<RedisCommands<String, String>> commands = (List<RedisCommands<String, String>>) resource;
            for (int i = 0; i < threadCount; i++) {
                // 轮询分配连接
                RedisCommands<String, String> cmd = commands.get(i % commands.size());
                executor.submit(new LettuceTask(cmd, opsPerThread, latch));
            }
        } else if (resource instanceof GenericObjectPool) { // Lettuce Pool
             for (int i = 0; i < threadCount; i++) {
                executor.submit(new LettucePoolTask((GenericObjectPool<StatefulRedisConnection<String, String>>) resource, opsPerThread, latch));
            }
        }

        latch.await();
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        long opsPerSecond = (duration > 0) ? (totalOps * 1000 / duration) : 0;
        System.out.printf("Result for   %-35s | Duration: %-6d ms | Ops/sec: %d%n%n", testName, duration, opsPerSecond);
        executor.shutdown();
    }
}

// --- 任务定义 ---

class JedisTask implements Runnable {
    private final JedisPool jedisPool;
    private final int ops;
    private final CountDownLatch latch;

    public JedisTask(JedisPool jedisPool, int ops, CountDownLatch latch) {
        this.jedisPool = jedisPool;
        this.ops = ops;
        this.latch = latch;
    }

    @Override
    public void run() {
        try (Jedis jedis = jedisPool.getResource()) {
            for (int i = 0; i < ops; i++) {
                String key = "jedis-key-" + Thread.currentThread().getId() + "-" + i;
                jedis.set(key, "value");
                jedis.get(key);
            }
        } finally {
            latch.countDown();
        }
    }
}

class LettuceTask implements Runnable {
    private final RedisCommands<String, String> commands;
    private final int ops;
    private final CountDownLatch latch;

    public LettuceTask(RedisCommands<String, String> commands, int ops, CountDownLatch latch) {
        this.commands = commands;
        this.ops = ops;
        this.latch = latch;
    }

    @Override
    public void run() {
        for (int i = 0; i < ops; i++) {
            String key = "lettuce-key-" + Thread.currentThread().getId() + "-" + i;
            commands.set(key, "value");
            commands.get(key);
        }
        latch.countDown();
    }
}

class LettucePoolTask implements Runnable {
    private final GenericObjectPool<StatefulRedisConnection<String, String>> pool;
    private final int ops;
    private final CountDownLatch latch;

    public LettucePoolTask(GenericObjectPool<StatefulRedisConnection<String, String>> pool, int ops, CountDownLatch latch) {
        this.pool = pool;
        this.ops = ops;
        this.latch = latch;
    }
    
    @Override
    public void run() {
        try (StatefulRedisConnection<String, String> connection = pool.borrowObject()) {
            RedisCommands<String, String> commands = connection.sync();
            for (int i = 0; i < ops; i++) {
                String key = "lettuce-pool-key-" + Thread.currentThread().getId() + "-" + i;
                commands.set(key, "value");
                commands.get(key);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            latch.countDown();
        }
    }
}