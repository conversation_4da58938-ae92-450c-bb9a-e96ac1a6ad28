import re



def extract_user_ids(input_file, output_file):
    # 使用正则表达式匹配 "userId: 数字"
    pattern = r'userId:\s*(\d+)'

    # 逐行读取大文件，避免一次性加载整个文件到内存
    with open(input_file, 'r', encoding='utf-8') as f_in, \
            open(output_file, 'w', encoding='utf-8') as f_out:
        for line in f_in:
            # 查找所有匹配项
            matches = re.findall(pattern, line)
            # 将找到的ID写入输出文件
            for user_id in matches:
                f_out.write(f"{user_id}\n")


if __name__ == "__main__":
    input_file = '/Users/<USER>/Documents/PersonalPrj/snippet/export-1e6d95ef-6fce-40a0-a1e3-23ca1aae03e2.json'
    output_file = '/Users/<USER>/Documents/PersonalPrj/snippet/user_ids.txt'

    print(f"开始处理文件: {input_file}")
    extract_user_ids(input_file, output_file)
    print(f"处理完成，结果已保存到: {output_file}")
