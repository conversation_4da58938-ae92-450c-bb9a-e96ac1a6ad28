import redis
import random
import concurrent.futures
import threading


def get_random_keys(redis_client, count=40):
    # 使用RANDOMKEY命令获取随机key
    keys = set()
    max_attempts = count * 2  # 设置最大尝试次数以避免无限循环
    attempts = 0

    while len(keys) < count and attempts < max_attempts:
        key = redis_client.randomkey()
        if key is not None:
            keys.add(key)
        attempts += 1

    return list(keys)


# 添加线程安全的打印锁
print_lock = threading.Lock()


def thread_safe_print(*args, **kwargs):
    with print_lock:
        print(*args, **kwargs)


def compare_redis_instances(source_host, target_host):
    # 解析host和port
    source_host, source_port = source_host.split(':')
    target_host, target_port = target_host.split(':')

    try:
        # 连接两个Redis实例
        source_redis = redis.Redis(
            host=source_host, port=int(source_port), socket_timeout=5)
        target_redis = redis.Redis(
            host=target_host, port=int(target_port), socket_timeout=5)

        # 获取随机key
        random_keys = get_random_keys(source_redis)

        # 打印实例信息和随机key列表
        thread_safe_print(f"\n源实例: {source_host}:{source_port}")
        thread_safe_print(f"目标实例: {target_host}:{target_port}")
        thread_safe_print(f"选取的随机key: {random_keys}")

        # 比较每个key的值，只输出不同的
        has_diff = False
        for key in random_keys:
            # 获取key的类型
            source_type = source_redis.type(key).decode()
            target_type = target_redis.type(
                key).decode() if target_redis.exists(key) else None

            # 根据类型获取值
            source_value = None
            target_value = None

            if source_type == 'string':
                source_value = source_redis.get(key)
                target_value = target_redis.get(
                    key) if target_type == 'string' else None
            elif source_type == 'hash':
                source_value = source_redis.hgetall(key)
                target_value = target_redis.hgetall(
                    key) if target_type == 'hash' else None
            elif source_type == 'list':
                source_value = source_redis.lrange(key, 0, -1)
                target_value = target_redis.lrange(
                    key, 0, -1) if target_type == 'list' else None
            elif source_type == 'set':
                source_value = source_redis.smembers(key)
                target_value = target_redis.smembers(
                    key) if target_type == 'set' else None
            elif source_type == 'zset':
                source_value = source_redis.zrange(key, 0, -1, withscores=True)
                target_value = target_redis.zrange(
                    key, 0, -1, withscores=True) if target_type == 'zset' else None

            if source_value != target_value:
                has_diff = True
                if target_value is None:
                    thread_safe_print(
                        f"Key '{key.decode()}' 在目标实例中不存在或类型不同 (源类型: {source_type}, 目标类型: {target_type})")
                else:
                    thread_safe_print(f"Key '{key.decode()}' 在两个实例中的值不同:")
                    thread_safe_print(
                        f"源实例值: {source_value} (类型: {source_type})")
                    thread_safe_print(
                        f"目标实例值: {target_value} (类型: {target_type})")

        if not has_diff:
            thread_safe_print("未发现差异")

    except Exception as e:
        thread_safe_print(
            f"比较实例 {source_host}:{source_port} -> {target_host}:{target_port} 时发生错误: {str(e)}")
    finally:
        # 关闭连接
        source_redis.close()
        target_redis.close()


def process_redis_mapping(file_path):
    unique_mappings = set()
    source_hosts = []
    target_hosts = []
    error_lines = []

    with open(file_path, 'r') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if not line:  # 跳过空行
                continue

            try:
                if ',' in line:
                    source_host, target_host = line.split(',')
                    source_host = source_host.strip()
                    target_host = target_host.strip()
                    mapping = (source_host, target_host)
                    if mapping not in unique_mappings:
                        unique_mappings.add(mapping)
                        source_hosts.append(source_host)
                        target_hosts.append(target_host)
                else:
                    error_lines.append((line_num, line))
            except Exception as e:
                error_lines.append((line_num, line))

    # 打印错误行信息
    if error_lines:
        print('\n发现格式错误的行：')
        for line_num, line in error_lines:
            print(f'第 {line_num} 行: {line}')

    return source_hosts, target_hosts


def main():
    file_path = 'r-uf6cng9rmsygpp9xz5.tsv'
    source_hosts, target_hosts = process_redis_mapping(file_path)

    thread_safe_print('\n开始比较Redis实例...')

    # 使用线程池并行处理比较任务
    with concurrent.futures.ThreadPoolExecutor(max_workers=30) as executor:
        # 创建比较任务列表
        futures = [executor.submit(compare_redis_instances, source_host, target_host)
                   for source_host, target_host in zip(source_hosts, target_hosts)]

        # 等待所有任务完成
        concurrent.futures.wait(futures)


if __name__ == "__main__":
    main()
