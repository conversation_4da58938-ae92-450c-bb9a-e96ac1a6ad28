
ALTER USER 'root'@'localhost' IDENTIFIED BY 'root' PASSWORD EXPIRE NEVER;  

ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY 'aloseeyouha';  

FLUSH PRIVILEGES; #刷新权限
	--创建新的用户：
	create user root@'%' identified WITH mysql_native_password BY 'root';
	grant all privileges on *.* to root@'%' with grant option;
	flush privileges;
	--在MySQL8.0创建用户并授权的语句则不被支持：
	mysql> grant all privileges on *.* to root@'%' identified by 'root' with grant option;
        ERROR 1064 (42000): You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'identified by 'root' with grant option' at line 1
        mysql> 

/usr/sbin/iptables -t nat -A POSTROUTING -s **********/24 ! -d **********/24 -j MASQUERADE
/usr/sbin/iptables -t nat -A POSTROUTING -s **********/24 -j MASQUERADE 
/usr/sbin/iptables -I INPUT -p tcp --dport 1194 -j ACCEPT
/usr/sbin/iptables -I FORWARD -s **********/24 -j ACCEPT
/usr/sbin/iptables -I FORWARD -m state --state RELATED,ESTABLISHED -j ACCEPT
ExecStop=/usr/sbin/iptables -t nat -D POSTROUTING -s **********/24 ! -d **********/24 -j MASQUERADE
ExecStop=/usr/sbin/iptables -D INPUT -p tcp --dport 1194 -j ACCEPT
ExecStop=/usr/sbin/iptables -D FOR
WARD -s **********/24 -j ACCEPT
ExecStop=/usr/sbin/iptables -D FORWARD -m state --state RELATED,ESTABLISHED -j ACCEPT

*******************************************************************************************************************************************************

阿里云key
LTAI5tHEyHWsW7E1tkAeNnfz	******************************

腾讯云
用户名：inner
密码：Finkainner000_
SecretId: AKIDjMn12xjHdmjCamtnV5VjlmeLrGXjjeQ1
SecretKey: VDukdSID5vhOwPp6JSo39PtQwgNxSqCS

AKIDufAPTHqAE4ex0nKaZHFzSebJlx0sYx2r	auxlJaaLq42jBNJyNWP2QYPzIL9gwwhV

https://docker.aityp.com/


sudo apt-get install gdisk -y
sudo apt-get install -y cloud-guest-utils
sudo LC_ALL=en_US.UTF-8 growpart /dev/vdb 1 
sudo xfs_growfs /data


kubectl annotate ingress live-web-gray-canary-ing nginx.ingress.kubernetes.io/canary-weight="0" --overwrite -n finka-backend-qa

python main.py --brokers=shanghai-kafka-4.cdb-dts.tencentcs.com.cn:32169 --topic=topic-subs-9r149kzl45-cynosdbmysql-n6smgqw4 --group=consumer-grp-subs-9r149kzl45-test-python --user=account-subs-9r149kzl45-finka-test --password=finkauser




echo -e '\n export ANTHROPIC_AUTH_TOKEN=sk-u3yWsCPOo6Luu8bkRmO4jJrDMQwQwnrmvrH5SQB8N9TpHVt6' >> ~/.bash_profile
echo -e '\n export ANTHROPIC_BASE_URL=https://anyrouter.top' >> ~/.bash_profile
echo -e '\n export ANTHROPIC_AUTH_TOKEN=sk-u3yWsCPOo6Luu8bkRmO4jJrDMQwQwnrmvrH5SQB8N9TpHVt6' >> ~/.bashrc
echo -e '\n export ANTHROPIC_BASE_URL=https://anyrouter.top' >> ~/.bashrc

echo -e '\n export ANTHROPIC_AUTH_TOKEN=sk-u3yWsCPOo6Luu8bkRmO4jJrDMQwQwnrmvrH5SQB8N9TpHVt6' >> ~/.zshrc
echo -e '\n export ANTHROPIC_BASE_URL=https://anyrouter.top' >> ~/.zshrc