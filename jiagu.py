# -*- coding: utf-8 -*-
from flask import Flask, request, send_from_directory
import subprocess
import hashlib
import os

app = Flask(__name__)
UPLOAD_FOLDER = '/home/<USER>/apk/apps'
DATA_FOLDER = '/home/<USER>/apk/data'
ALLOWED_EXTENSIONS = {'apk'}


def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1] in ALLOWED_EXTENSIONS


def process_file(filename):
    # 假设使用 `convert` 命令行工具处理图像，需要根据实际情况调整命令
    output_filename = os.path.join(
        DATA_FOLDER, 'temp_process_file')  # 指定输出文件名和扩展名
    # jiagu input.apk output.apk base.conf
    cmd = ['/home/<USER>/apk/newlegu/jiagu', filename,
           output_filename, '/home/<USER>/apk/newlegu/base.conf']
    try:
        subprocess.check_call(cmd)
        return output_filename
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {' '.join(cmd)}")
        print(f"错误码: {e.returncode}")
        print(f"错误输出: {e.output.decode('utf-8') if e.output else '无输出'}")
        print(f"标准错误: {e.stderr.decode('utf-8') if e.stderr else '无错误信息'}")
        return None


@app.route('/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        return 'No file part', 400
    file = request.files['file']
    if file.filename == '':
        return 'No selected file', 400
    if file and allowed_file(file.filename):
        filename = os.path.join(UPLOAD_FOLDER, file.filename)
        file.save(filename)
        processed_file = process_file(filename)
        if processed_file:
            with open(processed_file, 'rb') as f:
                file_content = f.read()
            md5_id = hashlib.md5(file_content).hexdigest()
            os.rename(processed_file, os.path.join(DATA_FOLDER, md5_id))
            return md5_id, 200
        else:
            return 'File processing failed', 500
    return 'Invalid file format', 400


@app.route('/files/<md5_id>', methods=['GET'])
def get_file(md5_id):
    try:
        return send_from_directory(DATA_FOLDER, md5_id, as_attachment=True)
    except Exception as e:
        return str(e), 404


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)