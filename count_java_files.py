import os


def count_java_files(folder_path):
    """
    Counts the number of .java files in the given folder and its subfolders.
    """
    count = 0
    for root, _, files in os.walk(folder_path):
        for file in files:
            if file.endswith('.java'):
                count += 1
    return count


if __name__ == "__main__":
    # You can change this to the desired folder path
    target_folder = input("请输入要统计的文件夹路径：")
    if os.path.isdir(target_folder):
        java_file_count = count_java_files(target_folder)
        print(f"文件夹 '{target_folder}' 中共有 {java_file_count} 个 Java 文件。")
    else:
        print(f"错误： '{target_folder}' 不是一个有效的文件夹路径。")
