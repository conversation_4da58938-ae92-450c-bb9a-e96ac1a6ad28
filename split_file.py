import os


def split_file(file_path, lines_per_file=5000):
    """
    Splits a large text file into smaller files, each containing a specified number of lines.

    Args:
        file_path (str): The path to the input file.
        lines_per_file (int): The maximum number of lines per output file.
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f_in:
            file_number = 1
            line_count = 0
            f_out = None

            for line in f_in:
                if line_count == 0:
                    output_file_name = f"{os.path.splitext(file_path)[0]}_part{file_number}.txt"
                    f_out = open(output_file_name, 'w', encoding='utf-8')
                    print(f"Creating new file: {output_file_name}")

                f_out.write(line)
                line_count += 1

                if line_count >= lines_per_file:
                    f_out.close()
                    line_count = 0
                    file_number += 1

            # Close the last file if it was opened
            if f_out and not f_out.closed:
                f_out.close()

        print(
            f"File splitting completed. Total files created: {file_number - 1 if line_count == 0 else file_number}")

    except FileNotFoundError:
        print(f"Error: The file '{file_path}' was not found.")
    except Exception as e:
        print(f"An error occurred: {e}")


if __name__ == "__main__":
    # Replace 'picurls.txt' with the actual path to your large file
    input_file = '/Users/<USER>/Documents/PersonalPrj/snippet/piccdn/picurls.txt'
    split_file(input_file)
