#!/bin/bash

# Redis Performance Benchmark Script
# Based on https://developer.aliyun.com/article/1064562

echo "=== Redis Performance Benchmark Script ==="
echo "Testing Jedis vs Lettuce performance with different connection modes"
echo "Make sure Redis server is running on localhost:6379"
echo ""

# Check if Redis is running
if ! redis-cli ping > /dev/null 2>&1; then
    echo "Error: Redis server is not running or not accessible on localhost:6379"
    echo "Please start Redis server first:"
    echo "  redis-server"
    exit 1
fi

echo "Redis server is running. Starting benchmark tests..."
echo ""

# Create results directory
mkdir -p results

# Build the project
echo "Building project..."
mvn clean compile

if [ $? -ne 0 ]; then
    echo "Error: Maven build failed. Please check dependencies."
    exit 1
fi

echo "Build successful. Starting benchmarks..."
echo ""

# Function to run individual benchmark
run_benchmark() {
    local class_name=$1
    local output_file=$2
    local description=$3
    
    echo "=== $description ==="
    echo "Running $class_name..."
    
    mvn exec:java -Dexec.mainClass="com.example.redis.benchmark.$class_name" \
        -Dexec.args="-rf json -rff results/$output_file"
    
    if [ $? -eq 0 ]; then
        echo "✓ $description completed successfully"
        echo "Results saved to: results/$output_file"
    else
        echo "✗ $description failed"
    fi
    echo ""
}

# Run individual benchmarks
run_benchmark "JedisPoolBenchmark" "jedis-pool-benchmark.json" "Jedis Connection Pool Benchmark"
run_benchmark "LettuceSingleConnectionBenchmark" "lettuce-single-connection.json" "Lettuce Single Connection Benchmark"
run_benchmark "LettuceConnectionPoolBenchmark" "lettuce-connection-pool.json" "Lettuce Connection Pool Benchmark"

# Run comprehensive benchmark
echo "=== Comprehensive Benchmark (All Scenarios) ==="
echo "Running comprehensive benchmark suite..."

mvn exec:java -Dexec.mainClass="com.example.redis.benchmark.RedisPerformanceBenchmarkSuite" \
    -Dexec.args="-rf json -rff results/comprehensive-benchmark.json"

if [ $? -eq 0 ]; then
    echo "✓ Comprehensive benchmark completed successfully"
else
    echo "✗ Comprehensive benchmark failed"
fi

echo ""
echo "=== Benchmark Results Summary ==="
echo "All benchmark results are saved in the 'results/' directory:"
ls -la results/

echo ""
echo "=== Performance Analysis Tips ==="
echo "1. Compare QPS (operations per second) between different connection modes"
echo "2. Analyze how connection pool size affects performance"
echo "3. Look at latency patterns for different Redis operations"
echo "4. Consider memory usage and CPU utilization"
echo ""
echo "Key findings from the referenced article:"
echo "- Jedis performs best with ~50 connections, degrades with 200+ connections"
echo "- Lettuce single connection mode offers consistent performance"
echo "- Connection pool configuration significantly impacts performance"
echo ""
echo "To generate visual reports, consider using JMH's built-in reporting or"
echo "tools like JMH Visualizer: https://jmh.morethan.io/"
echo ""
echo "Benchmark suite completed!"