# 挂载磁盘
fdisk -l
sudo mkfs -t ext4 /dev/vdb
sudo mount /dev/vdb /data

sudo cp -a /tmp/data_backup/* /data

sudo mkfs.xfs /dev/vdb
sudo mount /dev/vdb /data

sudo gpg --homedir /tmp --no-default-keyring --keyring /etc/apt/keyrings/scylladb.gpg --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys a43e06657bac99e3
sudo wget -O /etc/apt/sources.list.d/scylla.list https://downloads.scylladb.com/deb/debian/scylla-6.2.list
sudo apt-get update
sudo apt-get install -y scylla 
sudo mkdir -p /data/scylla/data
sudo mkdir -p /data/scylla/commitlog
sudo chown -R scylla:scylla /data/scylla


 scp -i ~/.ssh/aliyun-default-key.pem cassandra-rackdc.properties scylla.yaml ubuntu@10.250.1.54:/home/<USER>
